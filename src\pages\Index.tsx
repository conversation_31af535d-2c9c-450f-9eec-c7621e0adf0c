import React, { useState, useMemo, useRef, useEffect } from 'react';
import { Plus, Minus, ShoppingCart, Users, Star, Utensils, Search, X, ChevronLeft, ChevronRight, Home } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface MenuItem {
  id: string;
  name: string;
  price: number;
  description?: string;
  category: string;
  isVegetarian?: boolean;
  isSpicy?: boolean;
  isSignature?: boolean;
}

interface OrderItem extends MenuItem {
  quantity: number;
}

const menuData: MenuItem[] = [
  // Appetizers
  { id: 'app1', name: 'Butter Fried Onion Rings', price: 730, category: 'Appetizers' },
  { id: 'app2', name: 'Shredded Potato', price: 730, category: 'Appetizers' },

  // Soup
  { id: 'soup1', name: 'Chicken Soup', price: 850, category: 'Soup' },
  { id: 'soup2', name: 'Chicken with Sweet Corn Soup', price: 950, category: 'Soup' },
  { id: 'soup3', name: 'Vegetable Soup', price: 650, category: 'Soup', isVegetarian: true },
  { id: 'soup4', name: 'Tomato and Egg Soup', price: 790, category: 'Soup' },
  { id: 'soup5', name: 'Tom Yum (Sea Food Soup)', price: 990, category: 'Soup', isSpicy: true, description: 'Traditional hot and sour Thai soup with seafood' },

  // Salad
  { id: 'salad1', name: 'Coleslaw Salad', price: 930, category: 'Salad', isVegetarian: true },
  { id: 'salad2', name: 'Mix Vegetable Salad', price: 930, category: 'Salad', isVegetarian: true },
  { id: 'salad3', name: 'Tomato Onion Salad', price: 930, category: 'Salad', isVegetarian: true },
  { id: 'salad4', name: 'Chefs Special Salad', price: 1690, category: 'Salad', description: 'A refreshing blend of seasonal ingredients with our signature dressing' },

  // Rice Dishes
  { id: 'rice1', name: 'Vegetable Fried Rice', price: 790, category: 'Rice Dishes', isVegetarian: true },
  { id: 'rice2', name: 'Garlic Rice', price: 890, category: 'Rice Dishes', isVegetarian: true },
  { id: 'rice3', name: 'Egg Fried Rice', price: 890, category: 'Rice Dishes' },
  { id: 'rice4', name: 'Chicken Fried Rice', price: 1250, category: 'Rice Dishes' },
  { id: 'rice5', name: 'Sea Food Fried Rice', price: 1390, category: 'Rice Dishes' },
  { id: 'rice6', name: 'Steamed Rice', price: 590, category: 'Rice Dishes', isVegetarian: true },
  { id: 'rice7', name: 'Mixed Fried Rice', price: 1390, category: 'Rice Dishes' },
  { id: 'rice8', name: 'Chicken Mix Fried Rice', price: 1250, category: 'Rice Dishes' },
  { id: 'rice9', name: 'Fish Fried Rice', price: 1290, category: 'Rice Dishes' },
  { id: 'rice10', name: 'Beef Fried Rice', price: 1390, category: 'Rice Dishes' },

  // Noodles
  { id: 'noodle1', name: 'Vegetable Fried Noodles', price: 850, category: 'Noodles', isVegetarian: true },
  { id: 'noodle2', name: 'Garlic Noodles', price: 950, category: 'Noodles', isVegetarian: true },
  { id: 'noodle3', name: 'Egg Noodles', price: 950, category: 'Noodles' },
  { id: 'noodle4', name: 'Chicken Fried Noodles', price: 1300, category: 'Noodles' },
  { id: 'noodle5', name: 'Sea Food Fried Noodles', price: 1430, category: 'Noodles' },
  { id: 'noodle6', name: 'Steamed Noodles', price: 690, category: 'Noodles', isVegetarian: true },
  { id: 'noodle7', name: 'Mixed Fried Noodles', price: 1430, category: 'Noodles' },
  { id: 'noodle8', name: 'Fish Fried Noodles', price: 1400, category: 'Noodles' },
  { id: 'noodle9', name: 'Beef Noodles', price: 1430, category: 'Noodles' },

  // Oriental Dishes
  { id: 'oriental1', name: 'Nasigoreng Fried Rice', price: 1430, category: 'Oriental Dishes', isSignature: true, description: 'Indonesian-inspired fried rice with chicken, shrimp, vegetables, and a fried egg' },
  { id: 'oriental2', name: 'Chicken Biriyani', price: 1430, category: 'Oriental Dishes' },
  { id: 'oriental3', name: 'Mongolian Rice', price: 1430, category: 'Oriental Dishes' },
  { id: 'oriental4', name: 'Spice & Herb Special Rice or Noodles', price: 2350, category: 'Oriental Dishes', isSignature: true, description: 'Our signature dish featuring a premium mix of chicken, seafood, and vegetables' },

  // Chicken Dishes
  { id: 'chicken1', name: 'Devilled Chicken', price: 1430, category: 'Chicken Dishes', isSpicy: true },
  { id: 'chicken2', name: 'Chilli Chicken with Cashewnuts', price: 1530, category: 'Chicken Dishes', isSpicy: true },
  { id: 'chicken3', name: 'Sweet and Sour Chicken', price: 1530, category: 'Chicken Dishes' },
  { id: 'chicken4', name: 'Kankun with Garlic Chicken', price: 1530, category: 'Chicken Dishes' },
  { id: 'chicken5', name: 'Fried Chicken', price: 1530, category: 'Chicken Dishes', description: 'Our famous Kandy-style fried chicken' },
  { id: 'chicken6', name: 'KFC (Kandy Fried Chicken)', price: 1530, category: 'Chicken Dishes' },

  // Koththu (popular items)
  { id: 'kottu1', name: 'Vegetable Koththu', price: 990, category: 'Koththu', isVegetarian: true },
  { id: 'kottu2', name: 'Egg Koththu', price: 1100, category: 'Koththu' },
  { id: 'kottu3', name: 'Fish Koththu', price: 1250, category: 'Koththu' },
  { id: 'kottu4', name: 'Chicken Koththu', price: 1390, category: 'Koththu' },
  { id: 'kottu5', name: 'Cheese & Chicken Koththu', price: 1590, category: 'Koththu', isSignature: true, description: 'Our popular fusion dish combining traditional koththu with cheese' },
  { id: 'kottu6', name: 'Beef Koththu', price: 1490, category: 'Koththu' },

  // Beverages
  { id: 'bev1', name: 'Cup of Milk Tea or Coffee', price: 350, category: 'Beverages' },
  { id: 'bev2', name: 'Lime Juice', price: 390, category: 'Beverages', isVegetarian: true },
  { id: 'bev3', name: 'Pineapple/Papaya/Mango', price: 390, category: 'Beverages', isVegetarian: true },
  { id: 'bev4', name: 'Coke/Sprite/Fanta (400ml)', price: 350, category: 'Beverages' },
  { id: 'bev5', name: 'Mineral Water (500ml)', price: 180, category: 'Beverages' },

  // Desserts
  { id: 'dessert1', name: 'Watalappan', price: 450, category: 'Desserts', description: 'Traditional Sri Lankan coconut custard dessert with jaggery' },
  { id: 'dessert2', name: 'Cream Caramel', price: 450, category: 'Desserts' },
  { id: 'dessert3', name: 'Fresh Fruit Platter', price: 630, category: 'Desserts', isVegetarian: true },
  { id: 'dessert4', name: 'Ice Cream', price: 390, category: 'Desserts' }
];

const categories = ['All', 'Appetizers', 'Soup', 'Salad', 'Rice Dishes', 'Noodles', 'Oriental Dishes', 'Chicken Dishes', 'Koththu', 'Beverages', 'Desserts'];

const Index = () => {
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [cart, setCart] = useState<OrderItem[]>([]);
  const [showBillSplit, setShowBillSplit] = useState(false);
  const [numberOfPeople, setNumberOfPeople] = useState(2);
  const [searchTerm, setSearchTerm] = useState('');
  const [isCartExpanded, setIsCartExpanded] = useState(false);
  const categoryScrollRef = useRef<HTMLDivElement>(null);

  const filteredMenu = useMemo(() => {
    let filtered = selectedCategory === 'All'
      ? menuData
      : menuData.filter(item => item.category === selectedCategory);

    if (searchTerm) {
      filtered = filtered.filter(item =>
        item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.category.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    return filtered;
  }, [selectedCategory, searchTerm]);

  const addToCart = (item: MenuItem) => {
    setCart(prev => {
      const existingItem = prev.find(cartItem => cartItem.id === item.id);
      if (existingItem) {
        return prev.map(cartItem =>
          cartItem.id === item.id
            ? { ...cartItem, quantity: cartItem.quantity + 1 }
            : cartItem
        );
      }
      return [...prev, { ...item, quantity: 1 }];
    });
  };

  const removeFromCart = (itemId: string) => {
    setCart(prev => {
      const existingItem = prev.find(cartItem => cartItem.id === itemId);
      if (existingItem && existingItem.quantity > 1) {
        return prev.map(cartItem =>
          cartItem.id === itemId
            ? { ...cartItem, quantity: cartItem.quantity - 1 }
            : cartItem
        );
      }
      return prev.filter(cartItem => cartItem.id !== itemId);
    });
  };

  const clearSearch = () => {
    setSearchTerm('');
  };

  const scrollCategories = (direction: 'left' | 'right') => {
    if (categoryScrollRef.current) {
      const scrollAmount = 200;
      const currentScroll = categoryScrollRef.current.scrollLeft;
      const targetScroll = direction === 'left'
        ? currentScroll - scrollAmount
        : currentScroll + scrollAmount;

      categoryScrollRef.current.scrollTo({
        left: targetScroll,
        behavior: 'smooth'
      });
    }
  };

  // Toggle cart expansion on mobile
  const toggleCartExpansion = () => {
    setIsCartExpanded(!isCartExpanded);
  };

  // Initialize cart as minimized on mobile
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 1024) {
        setIsCartExpanded(false); // Always minimized on desktop (cart is separate)
      }
    };

    handleResize(); // Check on mount
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const subtotalAmount = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  const serviceFee = subtotalAmount * 0.1; // 10% service fee
  const totalAmount = subtotalAmount + serviceFee;
  const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
  const amountPerPerson = totalAmount / numberOfPeople;

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-white">
      {/* Header */}
      <div className="bg-gradient-to-r from-red-600 to-red-700 text-white shadow-xl sticky top-0 z-50">
        <div className="container mx-auto px-4 py-5 md:py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Utensils className="h-7 w-7 md:h-9 md:w-9 text-red-100" />
              <div>
                <h1 className="text-xl md:text-3xl font-bold tracking-tight">Spice & Herb</h1>
                <p className="text-red-100 text-xs md:text-sm font-medium">Premium Dining Experience</p>
              </div>
            </div>

            {/* Home Button */}
            <Button
              onClick={() => window.open('https://spiceandherbrestaurant.com', '_blank')}
              variant="outline"
              size="sm"
              className="border-red-300/50 text-red-600 hover:bg-red-500/20 hover:border-red-200 hover:text-white transition-all duration-200 font-medium"
            >
              <Home className="h-4 w-4 mr-1 md:mr-2" />
              <span className="hidden sm:inline">Home</span>
            </Button>
          </div>

          {/* Search Bar */}
          <div className="mt-5 md:mt-6">
            <div className="relative max-w-md mx-auto lg:mx-0">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-red-300 h-4 w-4" />
              <Input
                type="text"
                placeholder="Search menu items..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-10 bg-red-600/20 border-red-400/30 text-white placeholder:text-red-200 focus:bg-red-600/30 focus:border-red-300 h-11 md:h-12"
              />
              {searchTerm && (
                <Button
                  onClick={clearSearch}
                  variant="ghost"
                  size="sm"
                  className="absolute right-1 top-1/2 transform -translate-y-1/2 h-7 w-7 p-0 text-red-300 hover:text-white hover:bg-red-600/30 rounded-md"
                >
                  <X className="h-3 w-3" />
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Smart Cart for Web - Positioned after header */}
      <div className="hidden lg:block">
        <div className="w-80 fixed right-0 top-[120px] md:top-[140px] h-[calc(100vh-120px)] md:h-[calc(100vh-140px)] bg-white shadow-2xl border-l border-red-100 z-40 overflow-hidden">
          {/* Desktop Cart Header */}
          <div className="p-5 border-b bg-gradient-to-r from-red-50 to-red-100/30">
            <div className="flex items-center justify-start">
              <h3 className="font-bold text-red-600 flex items-center text-lg">
                <ShoppingCart className="h-5 w-5 mr-3" />
                Your Order
              </h3>
              {totalItems > 0 && (
                <span className="ml-3 bg-red-600 text-white text-xs px-2.5 py-1 rounded-full font-medium">
                  {totalItems}
                </span>
              )}
            </div>
            {cart.length > 0 && (
              <p className="text-sm text-gray-600 mt-2">Total: Rs. {totalAmount.toLocaleString()}/=</p>
            )}
          </div>

          {/* Desktop Cart Items */}
          <div className="flex-1 overflow-y-auto p-5 h-[calc(100%-300px)]">
            {cart.length === 0 ? (
              <div className="text-center py-10 text-gray-500">
                <ShoppingCart className="h-14 w-14 mx-auto mb-5 text-gray-300" />
                <p className="text-base font-medium">Your cart is empty</p>
                <p className="text-sm text-gray-400 mt-2">Start adding delicious items!</p>
              </div>
            ) : (
              <div className="space-y-3">
                {cart.map((item) => (
                  <div key={item.id} className="flex justify-between items-center p-4 bg-gradient-to-r from-red-50 to-red-50/50 rounded-xl border border-red-100 shadow-sm hover:shadow-md transition-all duration-200">
                    <div className="flex-1 min-w-0 pr-3">
                      <h4 className="font-semibold text-base truncate text-gray-800">{item.name}</h4>
                      <p className="text-red-600 text-sm mt-1 font-medium">Rs. {item.price.toLocaleString()}/= each</p>
                      <p className="text-gray-500 text-xs mt-1">Total: Rs. {(item.price * item.quantity).toLocaleString()}/=</p>
                    </div>
                    <div className="flex items-center space-x-3 ml-3">
                      <Button
                        size="sm"
                        variant="outline"
                        className="h-9 w-9 p-0 border-red-200 text-red-600 hover:bg-red-100 hover:border-red-300 transition-colors"
                        onClick={() => removeFromCart(item.id)}
                      >
                        <Minus className="h-4 w-4" />
                      </Button>
                      <span className="font-bold min-w-[24px] text-center text-base bg-white px-2 py-1 rounded border">{item.quantity}</span>
                      <Button
                        size="sm"
                        className="h-9 w-9 p-0 bg-red-600 hover:bg-red-700 transition-colors shadow-sm"
                        onClick={() => addToCart(item)}
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Desktop Cart Summary */}
          {cart.length > 0 && (
            <div className="border-t border-red-100 p-5 bg-gradient-to-r from-white to-red-50/30">
              <div className="space-y-3 mb-5">
                <div className="flex justify-between text-base text-gray-700">
                  <span>Subtotal:</span>
                  <span className="font-medium">Rs. {subtotalAmount.toLocaleString()}/=</span>
                </div>
                <div className="flex justify-between text-base text-gray-700">
                  <span>Service Fee (10%):</span>
                  <span className="font-medium">Rs. {serviceFee.toLocaleString()}/=</span>
                </div>
                <Separator className="my-3" />
                <div className="flex justify-between font-bold text-red-600 text-xl">
                  <span>Total:</span>
                  <span>Rs. {totalAmount.toLocaleString()}/=</span>
                </div>
              </div>

              <Button
                className="w-full bg-red-600 hover:bg-red-700 text-base py-4 font-semibold shadow-lg hover:shadow-xl transition-all duration-200"
                onClick={() => setShowBillSplit(true)}
              >
                <Users className="h-5 w-5 mr-2" />
                Split Bill
              </Button>
            </div>
          )}
        </div>
      </div>

      <div className="flex flex-col lg:flex-row min-h-screen pt-[120px] md:pt-[140px]">
        {/* Main Content */}
        <div className="flex-1 lg:mr-80 pb-32 lg:pb-0">
          {/* Category Filter with Arrow Navigation */}
          <div className="bg-white shadow-sm border-b z-40 w-full sticky top-[120px] md:top-[140px]">
            <div className="container mx-auto px-4 py-2 md:py-3">
              <div className="relative">
                {/* Left Arrow */}
                <Button
                  onClick={() => scrollCategories('left')}
                  variant="ghost"
                  size="sm"
                  className="absolute left-0 top-1/2 transform -translate-y-1/2 z-10 bg-white/90 hover:bg-white shadow-md md:hidden rounded-full h-8 w-8"
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>

                {/* Category Buttons */}
                <div
                  ref={categoryScrollRef}
                  className="flex gap-2 md:gap-3 overflow-x-auto scrollbar-hide px-4 md:px-0 [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]"
                >
                  {categories.map((category) => (
                    <Button
                      key={category}
                      variant={selectedCategory === category ? "default" : "outline"}
                      size="sm"
                      className={`whitespace-nowrap text-sm md:text-base flex-shrink-0 px-3 py-2 md:px-4 md:py-2.5 font-medium transition-all duration-200 ${
                        selectedCategory === category
                          ? 'bg-red-600 hover:bg-red-700 text-white shadow-md'
                          : 'border-red-200 text-red-600 hover:bg-red-50 hover:border-red-300'
                      }`}
                      onClick={() => setSelectedCategory(category)}
                    >
                      {category}
                    </Button>
                  ))}
                </div>

                {/* Right Arrow */}
                <Button
                  onClick={() => scrollCategories('right')}
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-1/2 transform -translate-y-1/2 z-10 bg-white/90 hover:bg-white shadow-md md:hidden rounded-full h-8 w-8"
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Search Results Info */}
          {searchTerm && (
            <div className="container mx-auto px-4 py-4">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <p className="text-sm text-blue-800 font-medium">
                  Found {filteredMenu.length} items for "{searchTerm}"
                  {filteredMenu.length === 0 && (
                    <span className="block mt-1 text-red-600">Try a different search term or browse categories above</span>
                  )}
                </p>
              </div>
            </div>
          )}

          {/* Menu Items */}
          <div className="container mx-auto px-4 py-6 pb-16">
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-5 md:gap-6">
              {filteredMenu.map((item) => (
                <Card key={item.id} className="group hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 border-red-100 hover:border-red-200 bg-white">
                  <CardContent className="p-6 md:p-8">
                    <div className="flex justify-between items-start mb-5 md:mb-6">
                      <h3 className="font-bold text-base md:text-xl text-gray-800 group-hover:text-red-600 transition-colors leading-tight">
                        {item.name}
                      </h3>
                      <div className="flex gap-2 ml-4 flex-shrink-0">
                        {item.isSignature && <Badge className="bg-red-600 text-white text-xs font-medium px-2 py-1"><Star className="h-2 w-2 md:h-3 md:w-3 mr-1" />Special</Badge>}
                        {item.isVegetarian && <Badge variant="outline" className="text-green-600 border-green-200 text-xs font-medium px-2 py-1">Veg</Badge>}
                        {item.isSpicy && <Badge variant="outline" className="text-red-600 border-red-200 text-xs font-medium px-2 py-1">🌶️ Spicy</Badge>}
                      </div>
                    </div>

                    {item.description && (
                      <p className="text-sm md:text-base text-gray-600 mb-6 md:mb-7 leading-relaxed">{item.description}</p>
                    )}

                    <div className="flex justify-between items-center pt-3">
                      <div className="flex flex-col">
                        <span className="text-xl md:text-2xl font-bold text-red-600">Rs. {item.price.toLocaleString()}/=</span>
                        <span className="text-xs md:text-sm text-gray-500">per serving</span>
                      </div>

                      {cart.find(cartItem => cartItem.id === item.id) ? (
                        <div className="flex items-center space-x-3 bg-gradient-to-r from-red-50 to-red-100 rounded-xl p-3 border border-red-200">
                          <Button
                            size="sm"
                            variant="outline"
                            className="h-9 w-9 md:h-10 md:w-10 p-0 border-red-300 text-red-600 hover:bg-red-200 transition-colors rounded-lg"
                            onClick={() => removeFromCart(item.id)}
                          >
                            <Minus className="h-4 w-4 md:h-5 md:w-5" />
                          </Button>
                          <span className="font-bold min-w-[28px] md:min-w-[32px] text-center text-base md:text-lg text-red-700">
                            {cart.find(cartItem => cartItem.id === item.id)?.quantity}
                          </span>
                          <Button
                            size="sm"
                            className="h-9 w-9 md:h-10 md:w-10 p-0 bg-red-600 hover:bg-red-700 transition-colors rounded-lg shadow-md"
                            onClick={() => addToCart(item)}
                          >
                            <Plus className="h-4 w-4 md:h-5 md:w-5" />
                          </Button>
                        </div>
                      ) : (
                        <Button
                          size="sm"
                          className="bg-red-600 hover:bg-red-700 text-white text-sm md:text-base px-5 md:px-6 py-2.5 md:py-3 font-semibold shadow-lg hover:shadow-xl transition-all duration-200 rounded-xl"
                          onClick={() => addToCart(item)}
                        >
                          <Plus className="h-4 w-4 md:h-5 md:w-5 mr-2" />
                          Add to Cart
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>

        {/* Mobile Smart Cart - Minimizable */}
        <div className="lg:hidden">
          <div className={`
            fixed bottom-0 left-0 right-0 z-50 bg-white shadow-2xl border-t border-red-100
            transition-all duration-300 ease-in-out
            ${isCartExpanded ? 'max-h-[80vh]' : 'max-h-[90px]'}
          `}>
            {/* Mobile Cart Header - Clickable to expand/minimize */}
            <div
              className="cursor-pointer p-4 active:bg-gray-50 transition-colors"
              onClick={toggleCartExpansion}
            >
              {/* Drag indicator */}
              <div className="w-12 h-1.5 bg-gray-300 rounded-full mx-auto mb-4"></div>

              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <h3 className="font-bold text-red-600 flex items-center text-lg">
                    <ShoppingCart className="h-5 w-5 mr-3" />
                    Your Order
                  </h3>
                  {totalItems > 0 && (
                    <span className="ml-3 bg-red-600 text-white text-xs px-3 py-1.5 rounded-full font-medium">
                      {totalItems}
                    </span>
                  )}
                </div>
                <div className="text-right">
                  {cart.length > 0 ? (
                    <div>
                      <p className="font-bold text-red-600 text-base leading-tight">Rs. {totalAmount.toLocaleString()}/=</p>
                      <p className="text-xs text-gray-500 font-medium">{totalItems} item{totalItems !== 1 ? 's' : ''}</p>
                    </div>
                  ) : (
                    <div>
                      <p className="text-sm text-gray-400 font-medium">Empty cart</p>
                      <p className="text-xs text-gray-400">Add items to start</p>
                    </div>
                  )}
                  {/* Expand/Minimize indicator */}
                  <div className="text-gray-400 mt-2">
                    {isCartExpanded ? (
                      <ChevronLeft className="h-4 w-4 rotate-90" />
                    ) : (
                      <ChevronRight className="h-4 w-4 -rotate-90" />
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Expandable Cart Content */}
            {isCartExpanded && (
              <div className="border-t border-red-100">
                {/* Cart Items */}
                <div className="overflow-y-auto p-6 max-h-[50vh]">
                  {cart.length === 0 ? (
                    <div className="text-center py-16 text-gray-500">
                      <ShoppingCart className="h-16 w-16 mx-auto mb-5 text-gray-300" />
                      <p className="text-base font-medium">Your cart is empty</p>
                      <p className="text-sm text-gray-400 mt-3">Start adding delicious items!</p>
                    </div>
                  ) : (
                    <div className="space-y-5">
                      {cart.map((item) => (
                        <div key={item.id} className="flex justify-between items-center p-5 bg-gradient-to-r from-red-50 to-red-50/50 rounded-xl border border-red-100 shadow-sm hover:shadow-md transition-all duration-200">
                          <div className="flex-1 min-w-0 pr-5">
                            <h4 className="font-semibold text-base truncate text-gray-800">{item.name}</h4>
                            <p className="text-red-600 text-sm mt-2 font-medium">Rs. {item.price.toLocaleString()}/= each</p>
                            <p className="text-gray-500 text-sm mt-1">Total: Rs. {(item.price * item.quantity).toLocaleString()}/=</p>
                          </div>
                          <div className="flex items-center space-x-3 ml-4">
                            <Button
                              size="sm"
                              variant="outline"
                              className="h-9 w-9 p-0 border-red-200 text-red-600 hover:bg-red-100 hover:border-red-300 transition-colors rounded-lg"
                              onClick={() => removeFromCart(item.id)}
                            >
                              <Minus className="h-4 w-4" />
                            </Button>
                            <span className="font-bold min-w-[28px] text-center text-base bg-white px-3 py-2 rounded-lg border shadow-sm">{item.quantity}</span>
                            <Button
                              size="sm"
                              className="h-9 w-9 p-0 bg-red-600 hover:bg-red-700 transition-colors shadow-md rounded-lg"
                              onClick={() => addToCart(item)}
                            >
                              <Plus className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* Mobile Cart Summary */}
                {cart.length > 0 && (
                  <div className="border-t border-red-100 p-6 bg-gradient-to-r from-white to-red-50/30">
                    <div className="space-y-5 mb-6">
                      <div className="flex justify-between text-base text-gray-700">
                        <span className="font-medium">Subtotal:</span>
                        <span className="font-semibold">Rs. {subtotalAmount.toLocaleString()}/=</span>
                      </div>
                      <div className="flex justify-between text-base text-gray-700">
                        <span className="font-medium">Service Fee (10%):</span>
                        <span className="font-semibold">Rs. {serviceFee.toLocaleString()}/=</span>
                      </div>
                      <Separator className="my-5" />
                      <div className="flex justify-between font-bold text-red-600 text-xl">
                        <span>Total:</span>
                        <span>Rs. {totalAmount.toLocaleString()}/=</span>
                      </div>
                    </div>

                    <Button
                      className="w-full bg-red-600 hover:bg-red-700 text-base py-4 font-semibold shadow-lg hover:shadow-xl transition-all duration-200 rounded-xl"
                      onClick={() => setShowBillSplit(true)}
                    >
                      <Users className="h-5 w-5 mr-3" />
                      Split Bill
                    </Button>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Enhanced Bill Split Dialog */}
      <Dialog open={showBillSplit} onOpenChange={setShowBillSplit}>
        <DialogContent className="max-w-lg mx-4 max-h-[90vh] overflow-y-auto">
          <DialogHeader className="pb-8">
            <DialogTitle className="text-2xl font-bold text-red-600 text-center">Split the Bill</DialogTitle>
            <p className="text-gray-600 text-center mt-3">Calculate how much each person should pay</p>
          </DialogHeader>

          <div className="space-y-10">
            <div>
              <Label htmlFor="people" className="text-base font-semibold text-gray-700 mb-4 block">Number of People</Label>
              <Input
                id="people"
                type="number"
                min="1"
                max="20"
                value={numberOfPeople}
                onChange={(e) => setNumberOfPeople(Math.max(1, parseInt(e.target.value) || 1))}
                className="text-lg p-5 border-red-200 focus:border-red-400 focus:ring-red-200 rounded-lg"
              />
            </div>

            <div className="bg-gradient-to-r from-red-50 to-red-100/50 p-7 rounded-xl border border-red-200 space-y-6">
              <div className="flex justify-between text-base">
                <span className="text-gray-700 font-medium">Subtotal:</span>
                <span className="font-semibold">Rs. {subtotalAmount.toLocaleString()}/=</span>
              </div>
              <div className="flex justify-between text-base">
                <span className="text-gray-700 font-medium">Service Fee (10%):</span>
                <span className="font-semibold">Rs. {serviceFee.toLocaleString()}/=</span>
              </div>
              <div className="flex justify-between text-lg">
                <span className="font-bold text-gray-800">Total Amount:</span>
                <span className="font-bold text-gray-800">Rs. {totalAmount.toLocaleString()}/=</span>
              </div>
              <div className="flex justify-between text-lg">
                <span className="font-bold text-gray-800">Number of People:</span>
                <span className="font-bold text-gray-800">{numberOfPeople}</span>
              </div>
              <Separator className="my-6" />
              <div className="flex justify-between text-xl bg-white p-6 rounded-xl border-2 border-red-300 shadow-sm">
                <span className="font-bold text-red-600">Amount per Person:</span>
                <span className="font-bold text-red-600">Rs. {Math.ceil(amountPerPerson).toLocaleString()}/=</span>
              </div>
            </div>

            <div className="text-center">
              <div className="bg-blue-50 border border-blue-200 p-6 rounded-xl">
                <p className="text-base text-blue-800 font-medium">
                  💡 Each person should pay <span className="font-bold text-red-600">Rs. {Math.ceil(amountPerPerson).toLocaleString()}/=</span>
                  {amountPerPerson !== Math.ceil(amountPerPerson) && (
                    <span className="block text-sm text-blue-600 mt-3">(rounded up for convenience)</span>
                  )}
                </p>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Index;
